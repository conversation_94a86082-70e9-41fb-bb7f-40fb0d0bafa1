<template>
  <div class="Label2">
    <div class="info-panel">
      <!-- 标题栏 -->
      <div class="header">
        <div class="title-section">
          <span class="icon" :class="[props.attackInfo.warnType == 3 ? '' : 'icon1']"></span>
          <span class="title" :title="props.attackInfo.alarmType">{{ props.attackInfo.alarmType }}</span>
        </div>
        <div class="status" @click="handleConfirm" :disabled="isConfirming">
          {{ isConfirming ? "确认中..." : "确认" }}
        </div>
      </div>

      <!-- 信息内容 -->
      <div class="content">
        <div class="info-row">
          <span class="label">攻击时间：</span>
          <span class="value valueTime">{{ props.attackInfo.time }}</span>
        </div>

        <div class="info-row dual-column">
          <div class="column">
            <span class="label">{{ props.attackInfo.warnType == 3 ? "风险等级：" : "事件等级：" }}</span>
            <span class="value" :class="getRiskLevelClass(props.attackInfo.riskLevel)">{{ props.attackInfo.riskLevel }}</span>
          </div>
          <div class="column">
            <span class="label">攻击状态：</span>
            <span class="value" :class="getAttackStatusClass(props.attackInfo.status)">{{ props.attackInfo.status }}</span>
          </div>
        </div>

        <div class="info-row dual-column">
          <div class="column">
            <span class="label">{{ props.attackInfo.warnType == 3 ? "攻击来源：" : "影响主机：" }}</span>
            <span class="value">{{ props.attackInfo.warnType == 3 ? props.attackInfo.source : props.attackInfo.targetHost }}</span>
          </div>
          <div class="column" v-show="props.attackInfo.warnType == 3">
            <span class="label">攻击主机：</span>
            <span class="value">{{ props.attackInfo.attackHost }}</span>
          </div>
        </div>

        <div class="info-row">
          <span class="label">{{ props.attackInfo.warnType == 3 ? "被攻击主机：" : "事件内容：" }}</span>
          <span class="value ellipsis-text" :title="props.attackInfo.warnType == 3 ? props.attackInfo.targetHost : props.attackInfo.description">{{
            props.attackInfo.warnType == 3 ? props.attackInfo.targetHost : props.attackInfo.description
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref } from "vue";
import { usedata } from "../store/data";

// 获取数据存储实例
const dataStore = usedata();

// 定义 props 接收攻击信息
const props = defineProps({
  attackInfo: {
    type: Object,
    default: () => ({
      alarmType: "",
      time: "",
      riskLevel: "",
      status: "",
      source: "",
      attackHost: "",
      targetHost: "",
      id: "", // 添加 id 字段
    }),
  },
});

// 定义 emits 用于向父组件发送事件
const emit = defineEmits(["confirmed"]);

// 确认状态
const isConfirming = ref(false);

// 处理确认按钮点击事件
const handleConfirm = async () => {
  if (isConfirming.value || !props.attackInfo.id) {
    return;
  }

  try {
    isConfirming.value = true;

    // 调用 getJqgj 函数
    const result = await dataStore.getJqgj(props.attackInfo.id);

    // 检查响应状态
    if (result && result.data.code === 200) {
      // 从 attackAlarmData 中移除这条数据
      const index = dataStore.attackAlarmData.findIndex((item) => item.id === props.attackInfo.id);
      if (index !== -1) {
        dataStore.attackAlarmData.splice(index, 1);
      }

      // 向父组件发送确认成功事件
      emit("confirmed", props.attackInfo.id);
    } else {
    }
  } catch (error) {
  } finally {
    isConfirming.value = false;
  }
};

// 直接使用 props 保持响应式
// 不要解构 props，这会破坏响应式连接

// 根据风险等级返回对应的样式类
const getRiskLevelClass = (riskLevel) => {
  if (!riskLevel) return "risk-unknown";

  const level = riskLevel.toString().toLowerCase();

  // 根据风险等级关键词匹配
  if (level.includes("低") || level.includes("low")) {
    return "risk-low";
  } else if (level.includes("中") || level.includes("medium")) {
    return "risk-medium";
  } else if (level.includes("高") || level.includes("high")) {
    return "risk-high";
  } else if (level.includes("严重") || level.includes("critical") || level.includes("危急")) {
    return "risk-critical";
  }

  // 如果包含数字，根据数值范围判断
  const numMatch = level.match(/\d+/);
  if (numMatch) {
    const num = parseInt(numMatch[0]);
    if (num <= 30) return "risk-low";
    if (num <= 60) return "risk-medium";
    if (num <= 80) return "risk-high";
    return "risk-critical";
  }

  return "risk-unknown";
};

// 根据攻击状态返回对应的样式类
const getAttackStatusClass = (status) => {
  if (!status) return "status-unknown";

  const statusStr = status.toString().toLowerCase();

  // 根据攻击状态关键词匹配
  if (statusStr.includes("成功") || statusStr.includes("success")) {
    return "status-success";
  } else if (statusStr.includes("阻断") || statusStr.includes("拦截") || statusStr.includes("block")) {
    return "status-blocked";
  } else if (statusStr.includes("检测") || statusStr.includes("发现") || statusStr.includes("detect")) {
    return "status-detected";
  } else if (statusStr.includes("警告") || statusStr.includes("warn")) {
    return "status-warning";
  } else if (statusStr.includes("失败") || statusStr.includes("fail")) {
    return "status-failed";
  } else if (statusStr.includes("待处理") || statusStr.includes("pending")) {
    return "status-pending";
  } else if (statusStr.includes("处理中") || statusStr.includes("processing")) {
    return "status-processing";
  } else if (statusStr.includes("已处理") || statusStr.includes("processed")) {
    return "status-processed";
  }

  return "status-unknown";
};
</script>

<style lang="less" scoped>
.Label2 {
  width: 351px;
  height: 155px;
  margin-left: 5%;
  font-family: "Microsoft YaHei", sans-serif;
}

.info-panel {
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 12px;
  padding: 0;
  color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  background: rgba(151, 195, 253, 0.06);
  height: 32px;
  padding-left: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.title-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 允许flex子元素收缩 */
}

.title-section .icon {
  margin-right: 8px;
}

.icon {
  width: 12px;
  height: 12px;
  background-image: url("../../public/img/z.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.icon1 {
  background-image: url("../../public/img/z1.png");
}

.title {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  max-width: 320px; /* 设置最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help; /* 鼠标悬浮时显示帮助图标 */
  transition: color 0.2s ease;
}

.title:hover {
  color: #32fefc; /* 悬浮时改变颜色 */
}

.status {
  cursor: pointer;
  width: 28px;
  height: 20px;
  margin-right: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1bfefd;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  &:hover {
    color: #00fffb;
    transform: scale(1.1);
  }
}

.content {
  padding: 0 16px;
  padding-top: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 13px;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #a0a0a0;
  flex-shrink: 0;
}

.value {
  color: #ffffff;
  flex: 1;
}

/* 省略号文本样式 */
.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help; /* 鼠标悬浮时显示帮助图标 */
  transition: color 0.2s ease;
}

.ellipsis-text:hover {
  color: #32fefc; /* 悬浮时改变颜色 */
}

/* 风险等级样式 */
.risk-low {
  color: #1bfefd;
  font-weight: 500;
}

.risk-medium {
  color: #ff9145;
  font-weight: 500;
}

.risk-high {
  color: #ff614d;
  font-weight: 600;
}

.risk-critical {
  color: #ff3333;
  font-weight: 700;
  text-shadow: 0 0 4px rgba(255, 51, 51, 0.5);
}

.risk-unknown {
  color: #ffffff;
  font-weight: 400;
}

/* 攻击状态样式 */
.status-success {
  color: #ff3333;
  font-weight: 600;
  text-shadow: 0 0 4px rgba(255, 51, 51, 0.3);
}

.status-blocked {
  color: #00ff88;
  font-weight: 600;
}

.status-detected {
  color: #00d4ff;
  font-weight: 500;
}

.status-warning {
  color: #ffaa00;
  font-weight: 500;
}

.status-failed {
  color: #ffffff;
  font-weight: 400;
}

.status-pending {
  color: #ffcc00;
  font-weight: 500;
}

.status-processing {
  color: #00aaff;
  font-weight: 500;
}

.status-processed {
  color: #00ff88;
  font-weight: 500;
}

.status-unknown {
  color: #ffffff;
  font-weight: 400;
}

.dual-column {
  display: flex;
  justify-content: space-between;
}

.column {
  display: flex;
  align-items: center;
  flex: 1;
}

.column .value {
  flex: 1;
}
</style>
