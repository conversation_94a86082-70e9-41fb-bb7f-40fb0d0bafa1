import * as THREE from "three";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";

// Diffuse ring shader definition
const diffuseRingShader = {
  vertexShader: `
    // Vertex shader for expanding ring effect
    uniform float uTime;
    uniform float uRingRadius;
    uniform float uPulseSpeed;
    uniform float uPulseAmplitude;

    varying vec2 vUv;
    varying vec3 vWorldPos;
    varying vec3 vNormalDir;
    varying float vDistanceFromCenter;

    void main() {
      vUv = uv;
      vNormalDir = normalize(normalMatrix * normal);

      // Calculate world position
      vec4 worldPos = modelMatrix * vec4(position, 1.0);
      vWorldPos = worldPos.xyz;

      // Calculate distance from center for ring effect
      vDistanceFromCenter = length(position.xy);

      // No vertex animation - all expansion effects handled in fragment shader
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,

  fragmentShader: `
    // Fragment shader for diffuse ring effect with expanding waves
    uniform float uTime;
    uniform vec3 uRingColor;
    uniform float uRingRadius;
    uniform float uRingWidth;
    uniform float uIntensity;
    uniform float uFadeDistance;
    uniform float uPulseSpeed;
    uniform float uPulseAmplitude;
    uniform float uOpacity;

    varying vec2 vUv;
    varying vec3 vWorldPos;
    varying vec3 vNormalDir;
    varying float vDistanceFromCenter;

    void main() {
      // Calculate distance from center (0,0) in UV space
      vec2 center = vec2(0.5, 0.5);
      float distanceFromCenter = length(vUv - center) * 2.0; // Normalize to 0-1 range

      // Create expanding wave effect instead of pulsing
      float waveTime = uTime * uPulseSpeed;
      float waveRadius = mod(waveTime, 2.0); // Wave expands from 0 to 2, then resets

      // Create multiple expanding rings for more dramatic effect
      float wave1 = waveRadius;
      float wave2 = mod(waveRadius + 0.5, 2.0); // Second wave offset by 0.5
      float wave3 = mod(waveRadius + 1.0, 2.0); // Third wave offset by 1.0

      // Calculate intensity for each wave
      float intensity1 = 0.0;
      float intensity2 = 0.0;
      float intensity3 = 0.0;

      // Wave 1 - 主要扩散环
      float dist1 = abs(distanceFromCenter - wave1);
      if (dist1 < uRingWidth) {
        float fadeOut = 1.0 - (waveRadius / 2.0); // Fade as wave expands
        fadeOut = pow(fadeOut, 0.8); // 减缓淡出速度，使环更持久
        intensity1 = (1.0 - smoothstep(0.0, uRingWidth * 0.8, dist1)) * fadeOut;
      }

      // Wave 2 - 第二个扩散环
      float dist2 = abs(distanceFromCenter - wave2);
      if (dist2 < uRingWidth) {
        float fadeOut2 = 1.0 - (mod(waveRadius + 0.5, 2.0) / 2.0);
        fadeOut2 = pow(fadeOut2, 0.8);
        intensity2 = (1.0 - smoothstep(0.0, uRingWidth * 0.8, dist2)) * fadeOut2 * 0.8; // 增加第二环强度
      }

      // Wave 3 - 第三个扩散环
      float dist3 = abs(distanceFromCenter - wave3);
      if (dist3 < uRingWidth) {
        float fadeOut3 = 1.0 - (mod(waveRadius + 1.0, 2.0) / 2.0);
        fadeOut3 = pow(fadeOut3, 0.8);
        intensity3 = (1.0 - smoothstep(0.0, uRingWidth * 0.8, dist3)) * fadeOut3 * 0.6; // 增加第三环强度
      }

      // Combine all waves
      float finalIntensity = max(max(intensity1, intensity2), intensity3) * uIntensity;

      // Add center glow that doesn't expand
      float centerGlow = 1.0 - smoothstep(0.0, 0.4, distanceFromCenter);
      centerGlow *= 0.4; // 增加中心发光强度

      // Combine expanding waves with center glow
      finalIntensity = max(finalIntensity, centerGlow);

      // Apply fresnel-like effect for better visibility
      vec3 viewDirection = normalize(cameraPosition - vWorldPos);
      float fresnel = 1.0 - abs(dot(vNormalDir, viewDirection));
      fresnel = pow(fresnel, 2.0);

      // Final color calculation
      float alpha = finalIntensity * uOpacity * (0.7 + 0.3 * fresnel);

      // Use CustomShaderMaterial's csm_DiffuseColor
      csm_DiffuseColor = vec4(uRingColor, alpha);
    }
  `,
};

/**
 * 平面标记工具类
 *
 * 用于在3D场景中创建各种类型的平面标记，如起点标记、终点标记等。
 * 支持自定义颜色、大小、透明度和朝向。
 * 现在默认使用CustomShaderMaterial的扩散光环效果，提供更好的视觉效果。
 *
 * 功能特性：
 * - 默认使用扩散光环效果，创建不断向外扩散的光圈动画
 * - 多重扩散波效果，增强视觉冲击力
 * - 可通过 useGlowEffect: false 禁用光环效果
 * - 支持动态属性更新和动画控制
 * - 兼容现有API，无需修改现有代码
 * - 自动资源管理和清理
 * - 坐标去重机制，防止在相同位置重复创建扩散光环
 *
 * 扩散效果说明：
 * - pulseSpeed: 控制扩散速度（值越小扩散越慢）
 * - ringWidth: 控制扩散环的宽度（值越小环越细）
 * - intensity: 控制扩散环的亮度强度
 * - ringRadius: 控制最大扩散范围
 *
 * 坐标去重机制：
 * - 自动检测相同坐标位置的标记，避免重复创建
 * - 支持坐标精度配置，默认保留4位小数
 * - 提供强制创建选项，绕过去重检查
 * - 自动管理标记引用计数，安全清理资源
 *
 * 使用示例：
 * ```javascript
 * import PlaneMarker from './PlaneMarker.js';
 *
 * // 创建起点标记（默认带扩散效果和去重检查）
 * const startMarker = PlaneMarker.createStartMarker(startPosition);
 * scene.add(startMarker);
 *
 * // 强制创建标记（绕过去重检查）
 * const forceMarker = PlaneMarker.createStartMarker(position, {
 *   forceCreate: true
 * });
 *
 * // 创建自定义扩散标记
 * const glowMarker = PlaneMarker.createGlowRingMarker(position, {
 *   ringColor: 0x00ffff,
 *   intensity: 1.5,
 *   pulseSpeed: 0.8, // 较慢的扩散
 *   ringWidth: 0.05  // 细扩散环
 * });
 *
 * // 清理标记（自动处理引用计数）
 * PlaneMarker.destroyMarker(startMarker);
 * ```
 */
class PlaneMarker {
  // 静态属性：坐标去重管理
  static _positionRegistry = new Map(); // 存储位置 -> 标记信息的映射
  static _coordinatePrecision = 4; // 坐标精度（小数位数）
  /**
   * 生成位置的唯一键
   * @param {THREE.Vector3} position - 位置向量
   * @returns {string} 位置的唯一键
   * @private
   */
  static _generatePositionKey(position) {
    const precision = this._coordinatePrecision;
    const x = position.x.toFixed(precision);
    const y = position.y.toFixed(precision);
    const z = position.z.toFixed(precision);
    return `${x},${y},${z}`;
  }

  /**
   * 检查位置是否已存在标记
   * @param {THREE.Vector3} position - 位置向量
   * @returns {Object|null} 如果存在返回标记信息，否则返回null
   * @private
   */
  static _checkExistingMarker(position) {
    const key = this._generatePositionKey(position);
    return this._positionRegistry.get(key) || null;
  }

  /**
   * 注册标记到位置注册表
   * @param {THREE.Vector3} position - 位置向量
   * @param {THREE.Mesh} marker - 标记对象
   * @param {string} type - 标记类型
   * @private
   */
  static _registerMarker(position, marker, type) {
    const key = this._generatePositionKey(position);
    const existing = this._positionRegistry.get(key);

    if (existing) {
      // 增加引用计数
      existing.refCount++;
      existing.markers.push(marker);
      console.log(`🔄 位置 ${key} 的标记引用计数增加到 ${existing.refCount}`);
    } else {
      // 创建新的注册项
      this._positionRegistry.set(key, {
        position: position.clone(),
        type: type,
        markers: [marker],
        refCount: 1,
        createdAt: Date.now(),
      });
      console.log(`📍 新位置 ${key} 已注册，类型: ${type}`);
    }

    // 在标记上存储位置键，用于后续清理
    marker.userData.positionKey = key;
  }

  /**
   * 从位置注册表中注销标记
   * @param {THREE.Mesh} marker - 标记对象
   * @private
   */
  static _unregisterMarker(marker) {
    if (!marker.userData.positionKey) {
      return;
    }

    const key = marker.userData.positionKey;
    const existing = this._positionRegistry.get(key);

    if (existing) {
      // 减少引用计数
      existing.refCount--;

      // 从标记列表中移除
      const markerIndex = existing.markers.indexOf(marker);
      if (markerIndex > -1) {
        existing.markers.splice(markerIndex, 1);
      }

      console.log(`🔄 位置 ${key} 的标记引用计数减少到 ${existing.refCount}`);

      // 如果引用计数为0，从注册表中移除
      if (existing.refCount <= 0) {
        this._positionRegistry.delete(key);
        console.log(`🗑️ 位置 ${key} 已从注册表中移除`);
      }
    }
  }

  /**
   * 获取或创建指定位置的标记
   * @param {THREE.Vector3} position - 位置向量
   * @param {string} markerType - 标记类型 ('start', 'end', 'custom', 'circle', 'glowRing', 'basic')
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 标记对象（可能是现有的或新创建的）
   * @private
   */
  static _getOrCreateMarker(position, markerType, options = {}) {
    // 检查是否强制创建新标记
    if (options.forceCreate) {
      console.log(`🔨 强制创建新标记，跳过去重检查`);
      return this._createNewMarker(position, markerType, options);
    }

    // 检查是否已存在相同位置的标记
    const existing = this._checkExistingMarker(position);

    if (existing && existing.type === markerType) {
      // 找到相同类型的现有标记，创建一个新的标记但隐藏它，让现有标记继续显示
      const newMarker = this._createNewMarker(position, markerType, {
        ...options,
        _isSharedPosition: true, // 标记这是共享位置
        _primaryMarker: existing.markers[0], // 引用主标记
      });

      // 隐藏新标记，因为主标记已经在显示
      newMarker.visible = false;

      // 在新标记上存储主标记的引用
      newMarker.userData.primaryMarker = existing.markers[0];
      newMarker.userData.isSharedMarker = true;

      console.log(`♻️ 创建共享位置标记: ${newMarker.name} (主标记: ${existing.markers[0].name})`);
      return newMarker;
    }

    // 创建新标记
    return this._createNewMarker(position, markerType, options);
  }

  /**
   * 创建新标记
   * @param {THREE.Vector3} position - 位置向量
   * @param {string} markerType - 标记类型
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 新创建的标记对象
   * @private
   */
  static _createNewMarker(position, markerType, options) {
    let marker;

    switch (markerType) {
      case "start":
        marker = this._createStartMarkerInternal(position, options);
        break;
      case "end":
        marker = this._createEndMarkerInternal(position, options);
        break;
      case "custom":
        marker = this._createCustomMarkerInternal(position, options);
        break;
      case "circle":
        marker = this._createCircleMarkerInternal(position, options);
        break;
      case "glowRing":
        marker = this.createGlowRingMarker(position, options);
        break;
      case "basic":
        marker = this._createPlaneMarker(position, options);
        break;
      default:
        console.warn(`未知的标记类型: ${markerType}`);
        marker = this._createCustomMarkerInternal(position, options);
    }

    if (marker) {
      // 注册新标记
      this._registerMarker(position, marker, markerType);
    }

    return marker;
  }

  /**
   * 创建起点标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 平面网格对象
   */
  static createStartMarker(position, options = {}) {
    // 使用去重机制
    return this._getOrCreateMarker(position, "start", options);
  }

  /**
   * 内部创建起点标记方法
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 平面网格对象
   * @private
   */
  static _createStartMarkerInternal(position, options = {}) {
    const defaultOptions = {
      color: 0xff0000, // 红色
      ringColor: 0xff4444, // 稍亮的红色光环
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `StartPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      return this.createGlowRingMarker(position, {
        ...config,
        ringRadius: 0.8, // 更大的扩散范围
        intensity: 2.2, // 更高的强度
        pulseSpeed: 0.8, // 较慢的扩散速度
        ringWidth: 0.2, // 更宽的扩散环
      });
    } else {
      return this._createPlaneMarker(position, config);
    }
  }

  /**
   * 创建终点标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 平面网格对象
   */
  static createEndMarker(position, options = {}) {
    // 使用去重机制
    return this._getOrCreateMarker(position, "end", options);
  }

  /**
   * 内部创建终点标记方法
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 平面网格对象
   * @private
   */
  static _createEndMarkerInternal(position, options = {}) {
    const defaultOptions = {
      color: 0x00ff00, // 绿色
      ringColor: 0x44ff44, // 稍亮的绿色光环
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `EndPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      return this.createGlowRingMarker(position, {
        ...config,
        ringRadius: 0.8, // 更大的扩散范围
        intensity: 2.2, // 更高的强度
        pulseSpeed: 0.9, // 稍快的扩散速度（绿色终点）
        ringWidth: 0.2, // 更宽的扩散环
      });
    } else {
      return this._createPlaneMarker(position, config);
    }
  }

  /**
   * 创建自定义标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 平面网格对象
   */
  static createCustomMarker(position, options = {}) {
    // 使用去重机制
    return this._getOrCreateMarker(position, "custom", options);
  }

  /**
   * 内部创建自定义标记方法
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 平面网格对象
   * @private
   */
  static _createCustomMarkerInternal(position, options = {}) {
    const defaultOptions = {
      color: 0xffffff, // 白色
      ringColor: 0x88ddff, // 浅蓝色光环
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `CustomPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      return this.createGlowRingMarker(position, {
        ...config,
        ringRadius: 0.7, // 中等扩散范围
        intensity: 1.8, // 中等强度
        pulseSpeed: 1.2, // 中等扩散速度
        ringWidth: 0.18, // 更宽的扩散环
      });
    } else {
      return this._createPlaneMarker(position, config);
    }
  }

  /**
   * 创建圆形标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 圆形平面网格对象
   */
  static createCircleMarker(position, options = {}) {
    // 使用去重机制
    return this._getOrCreateMarker(position, "circle", options);
  }

  /**
   * 内部创建圆形标记方法
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 圆形平面网格对象
   * @private
   */
  static _createCircleMarkerInternal(position, options = {}) {
    const defaultOptions = {
      color: 0xffff00, // 黄色
      ringColor: 0xffff88, // 浅黄色光环
      radius: 0.25,
      segments: 32, // 增加分段数以获得更好的shader效果
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `CirclePlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      // 使用光环效果
      return this.createGlowRingMarker(position, {
        ...config,
        size: config.radius * 2, // 将半径转换为尺寸
        ringRadius: 0.9, // 更大的扩散范围
        intensity: 2.0, // 更高的强度
        pulseSpeed: 1.1, // 较慢的扩散速度
        ringWidth: 0.22, // 更宽的扩散环
        segments: config.segments,
      });
    } else {
      // 使用传统材质
      const geometry = new THREE.CircleGeometry(config.radius, config.segments);

      const material = new THREE.MeshBasicMaterial({
        color: config.color,
        transparent: true,
        opacity: config.opacity,
        side: THREE.DoubleSide,
      });

      const plane = new THREE.Mesh(geometry, material);
      this._setPlanePositionAndOrientation(plane, position);
      plane.name = config.name;

      return plane;
    }
  }

  /**
   * 创建传统样式的标记（不使用光环效果）
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 传统样式的平面网格对象
   */
  static createBasicMarker(position, options = {}) {
    const defaultOptions = {
      color: 0xffffff,
      size: 0.5,
      opacity: 0.8,
      name: `BasicPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };
    return this._createPlaneMarker(position, config);
  }

  /**
   * 创建带扩散光环效果的标记
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 带光环效果的平面网格对象
   */
  static createGlowRingMarker(position, options = {}) {
    const defaultOptions = {
      color: 0xffffff, // 基础颜色
      ringColor: 0x00ffff, // 光环颜色
      size: 1.0, // 平面大小
      ringRadius: 0.6, // 光环半径 (0-1) - 现在用作最大扩散半径
      ringWidth: 0.25, // 光环宽度 - 增加以获得更明显的扩散环
      intensity: 2.0, // 光环强度 - 增加以获得更明显的效果
      fadeDistance: 0.3, // 淡出距离
      pulseSpeed: 1.0, // 扩散速度 - 降低以获得更慢的扩散
      pulseAmplitude: 0.1, // 保留用于兼容性，但在扩散效果中不使用
      opacity: 0.9, // 整体透明度 - 增加以获得更明显的效果
      segments: 32, // 几何体分段数（更高的值产生更平滑的效果）
      name: `GlowRingPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    // 创建圆形几何体（使用更多分段以获得更好的shader效果）
    const geometry = new THREE.CircleGeometry(config.size, config.segments);

    // 创建CustomShaderMaterial
    const material = new CustomShaderMaterial({
      baseMaterial: THREE.MeshBasicMaterial,
      vertexShader: diffuseRingShader.vertexShader,
      fragmentShader: diffuseRingShader.fragmentShader,
      uniforms: {
        uTime: { value: 0.0 },
        uRingColor: { value: new THREE.Color(config.ringColor) },
        uRingRadius: { value: config.ringRadius },
        uRingWidth: { value: config.ringWidth },
        uIntensity: { value: config.intensity },
        uFadeDistance: { value: config.fadeDistance },
        uPulseSpeed: { value: config.pulseSpeed },
        uPulseAmplitude: { value: config.pulseAmplitude },
        uOpacity: { value: config.opacity },
      },
      transparent: true,
      depthWrite: false,
      blending: THREE.AdditiveBlending, // 使用加法混合模式产生发光效果
      side: THREE.DoubleSide,
    });

    // 创建网格
    const plane = new THREE.Mesh(geometry, material);

    // 设置位置和朝向
    this._setPlanePositionAndOrientation(plane, position);

    // 设置名称
    plane.name = config.name;

    // 添加动画更新函数
    plane.onBeforeRender = () => {
      if (material.uniforms.uTime) {
        material.uniforms.uTime.value += 0.016; // 约60fps的时间增量
      }
    };

    // 存储配置以便后续更新
    plane.userData.glowConfig = config;
    plane.userData.isGlowRingMarker = true;

    return plane;
  }

  /**
   * 创建基础平面标记（内部方法）
   * @private
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} config - 配置对象
   * @returns {THREE.Mesh} 平面网格对象
   */
  static _createPlaneMarker(position, config) {
    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(config.size, config.size);

    // 创建平面材质
    const material = new THREE.MeshBasicMaterial({
      color: config.color,
      transparent: true,
      opacity: config.opacity,
      side: THREE.DoubleSide, // 双面显示
    });

    // 创建平面网格
    const plane = new THREE.Mesh(geometry, material);

    // 设置位置和朝向
    this._setPlanePositionAndOrientation(plane, position);

    // 设置名称
    plane.name = config.name;

    return plane;
  }

  /**
   * 设置平面位置和朝向（内部方法）
   * @private
   * @param {THREE.Mesh} plane - 平面对象
   * @param {THREE.Vector3} position - 位置向量
   */
  static _setPlanePositionAndOrientation(plane, position) {
    // 设置平面位置
    plane.position.copy(position);

    // 计算从地球中心指向位置的方向向量
    const direction = position.clone().normalize();

    // 让平面法向量指向地球中心外侧（朝向观察者）
    plane.lookAt(position.x + direction.x, position.y + direction.y, position.z + direction.z);
  }

  /**
   * 批量创建标记平面
   * @param {Array} positions - 位置数组
   * @param {Object} options - 配置选项
   * @returns {Array} 平面网格对象数组
   */
  static createMultipleMarkers(positions, options = {}) {
    const defaultOptions = {
      type: "custom", // 'start', 'end', 'custom', 'circle', 'glowRing', 'basic'
      color: 0xffffff,
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
    };

    const config = { ...defaultOptions, ...options };
    const markers = [];

    positions.forEach((position, index) => {
      const markerOptions = {
        ...config,
        name: `${config.type}Plane_${index}_${Date.now()}`,
      };

      let marker;
      switch (config.type) {
        case "start":
          marker = this.createStartMarker(position, markerOptions);
          break;
        case "end":
          marker = this.createEndMarker(position, markerOptions);
          break;
        case "circle":
          marker = this.createCircleMarker(position, markerOptions);
          break;
        case "glowRing":
          marker = this.createGlowRingMarker(position, markerOptions);
          break;
        case "basic":
          marker = this.createBasicMarker(position, markerOptions);
          break;
        default:
          marker = this.createCustomMarker(position, markerOptions);
      }

      markers.push(marker);
    });

    console.log(`🚀 PlaneMarker ~ createMultipleMarkers ~ 已创建 ${markers.length} 个标记`);
    return markers;
  }

  /**
   * 更新光环标记的属性
   * @param {THREE.Mesh} marker - 光环标记对象
   * @param {Object} properties - 要更新的属性
   */
  static updateGlowRingProperties(marker, properties = {}) {
    if (!marker || !marker.userData.isGlowRingMarker || !marker.material.uniforms) {
      console.warn("🚀 PlaneMarker ~ updateGlowRingProperties ~ 无效的光环标记对象");
      return;
    }

    const uniforms = marker.material.uniforms;

    // 更新可用的uniform属性
    if (properties.ringColor !== undefined) {
      uniforms.uRingColor.value.setHex(properties.ringColor);
    }
    if (properties.ringRadius !== undefined) {
      uniforms.uRingRadius.value = properties.ringRadius;
    }
    if (properties.ringWidth !== undefined) {
      uniforms.uRingWidth.value = properties.ringWidth;
    }
    if (properties.intensity !== undefined) {
      uniforms.uIntensity.value = properties.intensity;
    }
    if (properties.fadeDistance !== undefined) {
      uniforms.uFadeDistance.value = properties.fadeDistance;
    }
    if (properties.pulseSpeed !== undefined) {
      uniforms.uPulseSpeed.value = properties.pulseSpeed;
    }
    if (properties.pulseAmplitude !== undefined) {
      uniforms.uPulseAmplitude.value = properties.pulseAmplitude;
    }
    if (properties.opacity !== undefined) {
      uniforms.uOpacity.value = properties.opacity;
    }

    // 更新存储的配置
    Object.assign(marker.userData.glowConfig, properties);

    console.log(`🚀 PlaneMarker ~ updateGlowRingProperties ~ ${marker.name} 属性已更新:`, properties);
  }

  /**
   * 设置光环标记的动画状态
   * @param {THREE.Mesh} marker - 光环标记对象
   * @param {boolean} enabled - 是否启用动画
   */
  static setGlowRingAnimation(marker, enabled = true) {
    if (!marker || !marker.userData.isGlowRingMarker) {
      console.warn("🚀 PlaneMarker ~ setGlowRingAnimation ~ 无效的光环标记对象");
      return;
    }

    if (enabled) {
      // 启用动画
      marker.onBeforeRender = () => {
        if (marker.material.uniforms.uTime) {
          marker.material.uniforms.uTime.value += 0.016;
        }
      };
    } else {
      // 禁用动画
      marker.onBeforeRender = null;
    }

    console.log(`🚀 PlaneMarker ~ setGlowRingAnimation ~ ${marker.name} 动画${enabled ? "已启用" : "已禁用"}`);
  }

  /**
   * 更新标记颜色
   * @param {THREE.Mesh} marker - 标记对象
   * @param {number} color - 新颜色
   */
  static updateMarkerColor(marker, color) {
    if (marker && marker.material) {
      marker.material.color.setHex(color);
      console.log(`🚀 PlaneMarker ~ updateMarkerColor ~ ${marker.name} 颜色已更新为:`, color);
    }
  }

  /**
   * 更新标记透明度
   * @param {THREE.Mesh} marker - 标记对象
   * @param {number} opacity - 新透明度 (0-1)
   */
  static updateMarkerOpacity(marker, opacity) {
    if (marker && marker.material) {
      marker.material.opacity = Math.max(0, Math.min(1, opacity));
      console.log(`🚀 PlaneMarker ~ updateMarkerOpacity ~ ${marker.name} 透明度已更新为:`, opacity);
    }
  }

  /**
   * 销毁标记（清理资源）
   * @param {THREE.Mesh} marker - 标记对象
   */
  static destroyMarker(marker) {
    if (!marker) {
      return;
    }

    // 检查是否是共享标记
    if (marker.userData.isSharedMarker) {
      // 这是一个共享位置的隐藏标记，直接销毁即可
      console.log(`🗑️ 销毁共享位置标记: ${marker.name}`);
      this._destroyMarkerPhysically(marker);
      return;
    }

    // 检查是否有位置键，如果有则使用引用计数机制
    if (marker.userData.positionKey) {
      const key = marker.userData.positionKey;
      const existing = this._positionRegistry.get(key);

      if (existing && existing.refCount > 1) {
        // 还有其他飞线在使用这个位置，检查是否是主标记
        const isMainMarker = existing.markers[0] === marker;

        if (isMainMarker) {
          // 这是主标记，需要将显示权转移给下一个标记
          this._transferDisplayToNextMarker(existing, marker);
        }

        // 减少引用计数
        this._unregisterMarker(marker);
        console.log(`♻️ 标记仍被其他飞线使用，仅减少引用计数: ${marker.name} (剩余引用: ${existing.refCount - 1})`);

        // 如果不是主标记，直接销毁
        if (!isMainMarker) {
          this._destroyMarkerPhysically(marker);
        }
        return;
      }
    }

    // 引用计数为0或没有位置键，执行真正的销毁
    this._destroyMarkerPhysically(marker);
  }

  /**
   * 将显示权转移给下一个标记
   * @param {Object} registryEntry - 注册表条目
   * @param {THREE.Mesh} currentMainMarker - 当前主标记
   * @private
   */
  static _transferDisplayToNextMarker(registryEntry, currentMainMarker) {
    // 找到下一个可以显示的标记
    const nextMarker = registryEntry.markers.find((m) => m !== currentMainMarker && m.userData.isSharedMarker);

    if (nextMarker) {
      // 将下一个标记设为可见，并移除其共享标记状态
      nextMarker.visible = true;
      nextMarker.userData.isSharedMarker = false;
      delete nextMarker.userData.primaryMarker;

      // 更新注册表，将新标记设为第一个（主标记）
      const markerIndex = registryEntry.markers.indexOf(nextMarker);
      if (markerIndex > 0) {
        registryEntry.markers.splice(markerIndex, 1);
        registryEntry.markers.unshift(nextMarker);
      }

      console.log(`🔄 显示权已转移: ${currentMainMarker.name} -> ${nextMarker.name}`);
    }
  }

  /**
   * 物理销毁标记（内部方法）
   * @param {THREE.Mesh} marker - 标记对象
   * @private
   */
  static _destroyMarkerPhysically(marker) {
    if (!marker) {
      return;
    }

    // 从位置注册表中注销标记
    this._unregisterMarker(marker);

    // 停止动画（如果是光环标记）
    if (marker.userData.isGlowRingMarker) {
      marker.onBeforeRender = null;
    }

    // 清理几何体
    if (marker.geometry) {
      marker.geometry.dispose();
    }

    // 清理材质
    if (marker.material) {
      // 如果是CustomShaderMaterial，需要清理uniforms
      if (marker.material.uniforms) {
        Object.values(marker.material.uniforms).forEach((uniform) => {
          if (uniform.value && typeof uniform.value.dispose === "function") {
            uniform.value.dispose();
          }
        });
      }
      marker.material.dispose();
    }

    // 清理用户数据
    marker.userData = {};

    console.log(`🗑️ PlaneMarker ~ destroyMarker ~ ${marker.name} 已物理销毁`);
  }

  /**
   * 批量销毁标记
   * @param {Array} markers - 标记对象数组
   */
  static destroyMultipleMarkers(markers) {
    if (Array.isArray(markers)) {
      markers.forEach((marker) => this.destroyMarker(marker));
      console.log(`🚀 PlaneMarker ~ destroyMultipleMarkers ~ 已销毁 ${markers.length} 个标记`);
    }
  }

  /**
   * 获取光环标记的当前配置
   * @param {THREE.Mesh} marker - 光环标记对象
   * @returns {Object} 当前配置对象
   */
  static getGlowRingConfig(marker) {
    if (!marker || !marker.userData.isGlowRingMarker) {
      console.warn("🚀 PlaneMarker ~ getGlowRingConfig ~ 无效的光环标记对象");
      return null;
    }

    return { ...marker.userData.glowConfig };
  }

  /**
   * 获取位置注册表的统计信息
   * @returns {Object} 统计信息
   */
  static getRegistryStats() {
    const stats = {
      totalPositions: this._positionRegistry.size,
      totalMarkers: 0,
      positions: [],
    };

    this._positionRegistry.forEach((info, key) => {
      stats.totalMarkers += info.refCount;
      stats.positions.push({
        key: key,
        type: info.type,
        refCount: info.refCount,
        markersCount: info.markers.length,
        createdAt: new Date(info.createdAt).toLocaleString(),
      });
    });

    return stats;
  }

  /**
   * 清理所有位置注册表（调试用）
   */
  static clearRegistry() {
    const count = this._positionRegistry.size;
    this._positionRegistry.clear();
    console.log(`🗑️ 已清理位置注册表，移除了 ${count} 个位置记录`);
  }

  /**
   * 设置坐标精度
   * @param {number} precision - 坐标精度（小数位数）
   */
  static setCoordinatePrecision(precision) {
    if (precision < 0 || precision > 10) {
      console.warn("坐标精度应在0-10之间");
      return;
    }

    const oldPrecision = this._coordinatePrecision;
    this._coordinatePrecision = precision;

    console.log(`坐标精度已更新: ${oldPrecision} -> ${precision}`);

    // 如果有现有的注册表，建议清理重建
    if (this._positionRegistry.size > 0) {
      console.warn("⚠️ 坐标精度变更后，建议调用 clearRegistry() 清理现有注册表");
    }
  }

  /**
   * 检查指定位置是否有标记
   * @param {THREE.Vector3} position - 位置向量
   * @returns {boolean} 是否存在标记
   */
  static hasMarkerAtPosition(position) {
    return this._checkExistingMarker(position) !== null;
  }

  /**
   * 获取指定位置的标记信息
   * @param {THREE.Vector3} position - 位置向量
   * @returns {Object|null} 标记信息或null
   */
  static getMarkerInfoAtPosition(position) {
    return this._checkExistingMarker(position);
  }
}

export default PlaneMarker;
